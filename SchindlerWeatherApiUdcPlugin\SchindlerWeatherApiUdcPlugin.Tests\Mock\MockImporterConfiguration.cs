﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.UDC.PluginBase.V2.Interfaces;

namespace SchindlerWeatherApiUdcPlugin.Tests.Mock;

public class MockImporterConfiguration : IImporterConfiguration
{
    public Uri Endpoint { get; set; } = new ("https://arbitrary.com");
    public string ApiKey { get; } = new Guid().ToString();
    public List<Localisation> Localisations {  get; set; }
    public List<ILocationData> Locations { get; set; } =
    [
        new LocationData()
        {
            LocationId = 1,
            Address = "Muenchen",
            Latitude = 39.97,
            Longitude = -82.89,
            Country = "TestCountry"
        }
    ];

    public MockImporterConfiguration()
    {
        Localisations = Locations
            .Select(x => new Localisation(x))
            .Where(x => x.IsValid)
            .Distinct()
            .ToList();
    }
}


public sealed class LocationData : ILocationData
{
    public int LocationId { get; set; }
    public string BoxId { get; }
    public string Name { get; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string Country { get; set; }
    public string City { get; set; }
    public string ZipCode { get; set; }
    public string Address { get; set; }
    public Guid? EditionGuid { get; set; }
    public IEnumerable<string> Tags { get; set; }
    public IReadOnlyDictionary<string, string> CustomValues { get; set; }
}