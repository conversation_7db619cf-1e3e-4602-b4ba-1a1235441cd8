﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi
{
    public interface IWeatherDataApi
    {
        Task<Weather> RetrieveWeatherDataAsync(Uri endpoint, string apiKey, Localisation localization, CancellationToken cancellationToken = default);
    }
}
