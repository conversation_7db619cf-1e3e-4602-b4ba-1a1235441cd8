﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Net.Http;
using System.Threading;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Grassfish.UDC.PluginBase.Enums;
using Grassfish.UDC.PluginBase.V2;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin
{
    [Export(typeof(PluginBase<>))]
    public class Plugin : PluginBase<UdcElement>
    {
        public override Guid Guid => PluginMetadata.PluginGuid;
        public override byte VersionMajor => PluginMetadata.VersionMajor;
        public override byte VersionMinor => PluginMetadata.VersionMinor;
        public override string Name => PluginMetadata.Name;
        public override string NameShort => PluginMetadata.NameShort;
        public override DisplayType DisplayType => DisplayType.Wheather;
        public override bool RequiresLocationData => true;
        public override bool DisableCategoryFilter => true;
        public override bool DisableElementFilter => true;

        private static readonly HttpClient httpClient = new();

        public override IEnumerable<FeedDefinition> FeedDefinitions
                          => new List<FeedDefinition>
                          {
                              new(
                                  PluginMetadata.FeedGuid,
                                  "Import from URL",
                                  FeedDefinitionType.Web,
                                  FeedUpdateType.Full)
                          };

        private readonly ServiceProvider _serviceProvider;

        [ImportingConstructor]
        public Plugin()
        {
            var pluginLoggingDelegates = new PluginLoggingDelegates(
                LogError,
                LogWarn,
                LogInfo,
                LogDebug
            );

            var serviceCollection = DependencyInjection.CreateServiceCollection(pluginLoggingDelegates);
            _serviceProvider = serviceCollection.BuildServiceProvider();
        }

        public Plugin(ServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public override ImportResult<UdcElement> Import(CancellationToken cancellationToken,
            IImportParameters<ICustomer, IFeed, ILocationData> importParameters)
        {
            var logger = _serviceProvider.GetRequiredService<IPluginLogger>();

            try
            {
                var importer = _serviceProvider.GetRequiredService<IImporter>();
                var configurationLoader = _serviceProvider.GetRequiredService<IWeatherApiImporterConfigurationLoader>();

                var importerConfiguration = configurationLoader.Create(importParameters.LocationData, importParameters.FeedToImport.Source);

                logger.LogDebug($"Importing measurement data from {importParameters.FeedToImport.Name}");

                var result = importer.ImportAsync(importerConfiguration)
                    .GetAwaiter()
                    .GetResult();

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex);
                return new ImportResult<UdcElement> { Success = false, ErrorMessage = ex.Message };
            }
        }
    }
}
