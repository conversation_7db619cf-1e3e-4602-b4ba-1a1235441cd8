﻿using System.Collections.Generic;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models
{
    public class UdcWeatherDocument
    {
        public string LastForecaUpdateDateUtc { get; set; }

        public CurrentWeather Current { get; set; }

        public List<ForecastWeather> Forecasts { get; set; }
    }

    public class CurrentWeather
    {
        public string Time { get; set; }
        public string Symbol { get; set; }
        public string SymbolPhrase { get; set; }
        public int Temperature { get; set; }
        public int FeelsLikeTemp { get; set; }
        public int RelHumidity { get; set; }
        public int DewPoint { get; set; }
        public int WindSpeed { get; set; }
        public string WindDirString { get; set; }
        public int WindGust { get; set; }
        public int PrecipProb { get; set; }
        public double PrecipRate { get; set; }
        public int Cloudiness { get; set; }
        public int ThunderProb { get; set; }
        public int UvIndex { get; set; }
        public double Pressure { get; set; }
        public double Visibility { get; set; }
    }

    public class ForecastWeather
    {
        public string Date { get; set; }
        public string Symbol { get; set; }
        public string SymbolPhrase { get; set; }
        public int MaxTemp { get; set; }
        public int MinTemp { get; set; }
        public int MaxFeelsLikeTemp { get; set; }
        public int MinFeelsLikeTemp { get; set; }
        public int MaxRelHumidity { get; set; }
        public int MinRelHumidity { get; set; }
        public int MaxDewPoint { get; set; }
        public int MinDewPoint { get; set; }
        public double PrecipAccum { get; set; }
        public int MaxWindSpeed { get; set; }
        public int WindDir { get; set; }
        public int MaxWindGust { get; set; }
        public int PrecipProb { get; set; }
        public int Cloudiness { get; set; }
        public string Sunrise { get; set; }
        public string Sunset { get; set; }
        public int SunriseEpoch { get; set; }
        public int SunsetEpoch { get; set; }
        public string Moonrise { get; set; }
        public string Moonset { get; set; }
        public int MoonPhase { get; set; }
        public int UvIndex { get; set; }
        public int MinVisibility { get; set; }
        public int Pressure { get; set; }
    }
}
