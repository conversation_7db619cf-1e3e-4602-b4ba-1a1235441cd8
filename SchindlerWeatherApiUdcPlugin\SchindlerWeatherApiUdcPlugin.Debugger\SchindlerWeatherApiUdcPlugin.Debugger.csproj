﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Grassfish.UDC.PluginBase" Version="1.0.2"/>
		<PackageReference Include="Grassfish.UDC.UdcFramework" Version="1.0.0"/>
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0"/>
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0"/>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SchindlerWeatherApiUdcPlugin\SchindlerWeatherApiUdcPlugin.csproj" />
		<Reference Include="System.Net.Http" />
	</ItemGroup>

	<ItemGroup>
		<None Update="config.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	
</Project>
