﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Mappings;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Factories
{
    public class UdcWeatherDocumentFactory : IUdcWeatherDocumentFactory
    {
        public UdcWeatherDocument Create(Weather importedWeather)
        {
            var result = new UdcWeatherDocument()
            {
                LastForecaUpdateDateUtc = DateTime.UtcNow.ToString("o", CultureInfo.InvariantCulture),
                Current = new CurrentWeather()
                {
                    Time = importedWeather.Location.LocalTime.ToString(),
                    Symbol = WeatherSymbolMapper.MapWeatherApiCodeToForecaCode(importedWeather.Current.Condition.Code),
                    SymbolPhrase = importedWeather.Current.Condition.Text,
                    Temperature = (int)importedWeather.Current.Temp,
                    FeelsLikeTemp = (int)importedWeather.Current.FeelsLikeTemp,
                    RelHumidity = importedWeather.Current.Humidity,
                    DewPoint = (int)importedWeather.Current.DewPoint,
                    WindSpeed = (int)importedWeather.Current.WindSpeed,
                    WindDirString = importedWeather.Current.WindDirString,
                    WindGust = (int)importedWeather.Current.WindGust,
                    PrecipProb = (int)importedWeather.Forecast.ForecastDays[0].Hours.Average(x => x.ChanceOfRain),
                    PrecipRate = importedWeather.Current.Precip,
                    Cloudiness = importedWeather.Current.Cloudiness,
                    //ThunderProb = ,
                    UvIndex = (int)importedWeather.Current.UvIndex,
                    Pressure = importedWeather.Current.Pressure,
                    Visibility = importedWeather.Current.Visibility
                }
            };

            importedWeather.Forecast.ForecastDays = importedWeather.Forecast.ForecastDays.Skip(1).ToArray();

            var forecastWeather = new List<ForecastWeather>();

            foreach (var forecastedDay in importedWeather.Forecast.ForecastDays)
            {
                var tempForecastDay = new ForecastWeather()
                {
                    Date = forecastedDay.Date,
                    Symbol = WeatherSymbolMapper.MapWeatherApiCodeToForecaCode(forecastedDay.Day.Condition.Code),
                    SymbolPhrase = forecastedDay.Day.Condition.Text,
                    MaxTemp = (int)forecastedDay.Day.MaxTemp,
                    MinTemp = (int)forecastedDay.Day.MinTemp,
                    MaxFeelsLikeTemp = (int)forecastedDay.Hours.Max(x => x.FeelsLikeTemp),
                    MinFeelsLikeTemp = (int)forecastedDay.Hours.Min(x => x.FeelsLikeTemp),
                    MaxRelHumidity = forecastedDay.Hours.Max(x => x.Humidity),
                    MinRelHumidity = forecastedDay.Hours.Min(x => x.Humidity),
                    MaxDewPoint = (int)forecastedDay.Hours.Max(x => x.DewPoint),
                    MinDewPoint = (int)forecastedDay.Hours.Min(x => x.DewPoint),
                    PrecipAccum = forecastedDay.Day.PrecipAccum,
                    MaxWindSpeed = (int)forecastedDay.Day.MaxWindSpeed,
                    WindDir = (int)forecastedDay.Hours.Average(x => x.WindDegree),
                    MaxWindGust = (int)forecastedDay.Hours.Average(x => x.WindGust),
                    PrecipProb = (int)forecastedDay.Hours.Average(x => x.ChanceOfRain),
                    Cloudiness = (int)forecastedDay.Hours.Average(x => x.Cloudiness),
                    Sunrise = forecastedDay.Astro.Sunrise,
                    Sunset = forecastedDay.Astro.Sunset,
                    //SunriseEpoch = ,
                    //SunsetEpoch = ,
                    Moonrise = forecastedDay.Astro.Moonrise,
                    Moonset = forecastedDay.Astro.Moonset,
                    //MoonPhase = ,
                    UvIndex = (int)forecastedDay.Day.UvIndex,
                    MinVisibility = (int)forecastedDay.Hours.Min(x => x.Visibility),
                    Pressure = (int)forecastedDay.Hours.Average(x => x.Pressure)
                };

                forecastWeather.Add(tempForecastDay);
            }

            result.Forecasts = forecastWeather;

            return result;
        }
    }
}
