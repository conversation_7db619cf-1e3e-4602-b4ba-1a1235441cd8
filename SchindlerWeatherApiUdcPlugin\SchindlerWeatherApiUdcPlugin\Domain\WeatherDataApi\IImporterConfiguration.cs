﻿using System;
using System.Collections.Generic;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.UDC.PluginBase.V2.Interfaces;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;

public interface IImporterConfiguration
{
    Uri Endpoint { get; set; }
    string ApiKey { get; }
    List<Localisation> Localisations {  get; set; } 
    List<ILocationData> Locations { get; set; }
}