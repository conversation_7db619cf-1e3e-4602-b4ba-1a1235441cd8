using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi
{
    public class WeatherDataApiConfigurationDto : IImporterConfiguration
    {
        public WeatherDataApiConfigurationDto(string endpoint, string apiKey, List<Localisation> localisation)
        {
            Endpoint = new Uri(endpoint);
            ApiKey = apiKey;
            Localisations = localisation;
        }

        public WeatherDataApiConfigurationDto() { }

        public Uri Endpoint { get; set; }
        public string ApiKey { get; set; }

        [JsonIgnore]
        public List<Localisation> Localisations {  get; set; }

        [JsonIgnore]
        public List<ILocationData> Locations { get; set; }
    }
}
