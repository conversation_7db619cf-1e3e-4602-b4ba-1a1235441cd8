using Grassfish.UDC.PluginBase.Enums;
using Grassfish.UDC.PluginBase.V2.Interfaces;

namespace SchindlerWeatherApiUdcPlugin.Tests.Mock;

public record MockFeed : IFeed
{
    public Guid DefinitionGuid => new Guid("82c09a01-b814-4c7a-8ddc-a4457c07814f");
    public string Name { get; } = string.Empty;
    public FeedDefinitionType Type => FeedDefinitionType.Web;
    public FeedUpdateType UpdateType => FeedUpdateType.Full;
    public string Source => "https://arbitrary.com";
    public string Username => string.Empty;
    public string Password => string.Empty;
    public string SpecificValue { get; } = string.Empty;
}
