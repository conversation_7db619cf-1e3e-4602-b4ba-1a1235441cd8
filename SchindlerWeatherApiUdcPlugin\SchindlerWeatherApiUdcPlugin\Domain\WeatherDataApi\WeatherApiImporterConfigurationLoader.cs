﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi
{
    public class WeatherApiImporterConfigurationLoader : IWeatherApiImporterConfigurationLoader
    {
        private readonly IServiceProvider _serviceProvider;

        public WeatherApiImporterConfigurationLoader(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }
        
        public WeatherDataApiConfigurationDto Create(IEnumerable<ILocationData> locations, string configurationSource)
        {
            var logger = _serviceProvider.GetRequiredService<IPluginLogger>();
            
            try
            {
                if (locations == null)
                {
                    logger.LogWarning("No locations were provided");

                    throw new ArgumentNullException(nameof(locations));
                }
                
                var loadSourceConfiguration = LoadUdcConfigurations(configurationSource);
                loadSourceConfiguration.Locations = locations?.ToList();

                var localisations = locations
                    .Select(x => new Localisation(x))
                    .Where(x => x.IsValid)
                    .Distinct()
                    .ToList();

                logger.LogInfo($"Got <{localisations.Count}> localisations");
                
                loadSourceConfiguration.Localisations = localisations;
                
                return loadSourceConfiguration;
            }
            catch (Exception exception)
            {
                throw new Exception($"Error occurred on loading configuration from source {configurationSource}", exception);
            }
        }

        private static WeatherDataApiConfigurationDto LoadUdcConfigurations(string source)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));

            if (!File.Exists(source))
            {
                throw new ConfigurationSourceNotFoundException($"Configuration file does not exist or is not reachable in path {source}");
            }

            try
            {
                var content = File.ReadAllText(source);

                return JsonConvert.DeserializeObject<WeatherDataApiConfigurationDto>(content);
            }
            catch (Exception exception)
            {
                throw new Exception($"Error occurred on deserializing configuration from source {source}", exception);
            }
        }
    }
}
