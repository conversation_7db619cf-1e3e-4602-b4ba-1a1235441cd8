﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35027.167
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SchindlerWeatherApiUdcPlugin", "SchindlerWeatherApiUdcPlugin\SchindlerWeatherApiUdcPlugin.csproj", "{9B712521-62BE-45DC-92BC-40E21ADBFC8C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SchindlerWeatherApiUdcPlugin.Debugger", "SchindlerWeatherApiUdcPlugin.Debugger\SchindlerWeatherApiUdcPlugin.Debugger.csproj", "{FA1BEC1C-D24B-49C2-A5BE-153CDFDEE419}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SchindlerWeatherApiUdcPlugin.Tests", "SchindlerWeatherApiUdcPlugin.Tests\SchindlerWeatherApiUdcPlugin.Tests.csproj", "{594C294B-09E9-43C1-B675-F30AF09E903D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9B712521-62BE-45DC-92BC-40E21ADBFC8C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B712521-62BE-45DC-92BC-40E21ADBFC8C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B712521-62BE-45DC-92BC-40E21ADBFC8C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B712521-62BE-45DC-92BC-40E21ADBFC8C}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA1BEC1C-D24B-49C2-A5BE-153CDFDEE419}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA1BEC1C-D24B-49C2-A5BE-153CDFDEE419}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA1BEC1C-D24B-49C2-A5BE-153CDFDEE419}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA1BEC1C-D24B-49C2-A5BE-153CDFDEE419}.Release|Any CPU.Build.0 = Release|Any CPU
		{594C294B-09E9-43C1-B675-F30AF09E903D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{594C294B-09E9-43C1-B675-F30AF09E903D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{594C294B-09E9-43C1-B675-F30AF09E903D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{594C294B-09E9-43C1-B675-F30AF09E903D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {532AAC6C-1E02-4307-98E7-0BFF00BC3BB5}
	EndGlobalSection
EndGlobal
