﻿namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer;

using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Factories;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Grassfish.UDC.PluginBase.V2;
using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

public class Importer : IImporter
{
    private readonly IWeatherDataApi _weatherDataApi;
    private readonly IPluginLogger _logger;
    private readonly IUdcWeatherDocumentFactory _udcWeatherDocumentFactory;

    public Importer(
        IWeatherDataApi weatherDataApi,
        IPluginLogger logger,
        IUdcWeatherDocumentFactory udcWeatherDocumentFactory)
    {
        _weatherDataApi = weatherDataApi;
        _logger = logger;
        _udcWeatherDocumentFactory = udcWeatherDocumentFactory;
    }

    public async Task<ImportResult<UdcElement>> ImportAsync(WeatherDataApiConfigurationDto weatherDataApiConfiguration)
    {
        try
        {
            var result = new ImportResult<UdcElement>();

            foreach (var localisation in weatherDataApiConfiguration.Localisations)
            {
                var weatherData = await _weatherDataApi.RetrieveWeatherDataAsync(
                    weatherDataApiConfiguration.Endpoint, 
                    weatherDataApiConfiguration.ApiKey, 
                    localisation);
                
                if(weatherData == null) continue;

                var countryCategory = new Category(localisation.Country, localisation.Country);

                result.StoreCategory(countryCategory, true);

                var elementId = localisation.ToString();
                var elementName = localisation.ToString();

                var udcElement = new UdcElement(elementId, elementName)
                {
                    Latitude = localisation.Lat,
                    Longitude = localisation.Long,
                    Document = _udcWeatherDocumentFactory.Create(weatherData)
                };

                udcElement.Document.LastForecaUpdateDateUtc = DateTime.UtcNow.ToString("o", CultureInfo.InvariantCulture);

                if (!weatherDataApiConfiguration.Locations.Any())
                {
                    _logger.LogWarning("There are no locations configured.");

                    continue;
                }

                var locationIds = weatherDataApiConfiguration.Locations?.Where(x => x.Latitude == localisation.Lat && x.Longitude == localisation.Long).Select(x => x.LocationId).ToList();

                _logger.LogInfo("Attempting to assign players to element. Element <{0}> Players <{1}>", elementId, string.Join(",", locationIds));
                locationIds.ForEach(udcElement.AddLocation);

                udcElement.AddCategory(countryCategory);
                result.StoreElement(udcElement);
            }

            //logging for the end
            _logger.LogDebug($"Import elements finished!");

            result.Success = true;
            return result;
        }
        catch (DomainException ex)
        {
            _logger.LogError("Caught exception while attempting to import the measurements.", ex);
            return new ImportResult<UdcElement>()
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }
}
