﻿namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models
{
    using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Extensions;
    using Grassfish.UDC.PluginBase.Enums;
    using Grassfish.UDC.PluginBase.V2;
    using Grassfish.UDC.PluginBase.V2.Attributes;

    public sealed class UdcElement : ElementBase
    {
        public UdcElement(string externalId, string name) : base(externalId?.Truncate(255), name.Truncate(100))
        {
        }

        [UdcProperty("2699de53-979c-4cdb-a763-20c3a7a997b4")]
        public double Latitude { get; set; }

        [UdcProperty("a41e1e56-c976-4276-b572-348ccbe2f510")]
        public double Longitude { get; set; }

        [UdcProperty("05d535f3-d74e-453d-9729-33207c0f5741", DefinitionType = ElementPropertyDefinitionType.Json)]
        public UdcWeatherDocument Document { get; set; }
        
        public static UdcElement Create()
        {
            return null;
        }
    }
}
