﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SchindlerWeatherApiUdcPlugin\SchindlerWeatherApiUdcPlugin.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="FakeItEasy" Version="8.3.0" />
		<PackageReference Include="FluentAssertions" Version="6.12.1" />
		<PackageReference Include="Grassfish.UDC.PluginBase" Version="1.0.2" />
		<PackageReference Include="JUnitTestLogger" Version="1.1.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
		<PackageReference Include="xunit" Version="2.9.1" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Properties\" />
	</ItemGroup>

	<ItemGroup>
		<None Update="TestData\*.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
