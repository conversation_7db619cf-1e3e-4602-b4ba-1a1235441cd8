﻿using FakeItEasy;
using FluentAssertions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Grassfish.UDC.PluginBase.Enums;
using Grassfish.UDC.PluginBase.V2;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using SchindlerWeatherApiUdcPlugin.Tests.Mock;
using Xunit;

namespace SchindlerWeatherApiUdcPlugin.Tests;

class TestImportParameters : IImportParameters<ICustomer?, IFeed?, ILocationData?>
{
    public ICustomer? Customer => null;
    public IFeed? FeedToImport => new MockFeed();
    public string WorkingDirectory { get; } = string.Empty;
    public IEnumerable<ILocationData>? LocationData => [ 
        new LocationData() 
        { 
        LocationId = 1,
        Address = "Muenchen",
        Latitude = 50.113754,
        Longitude = 8.661072,
        Country = "TestCountry"
    }];
}

public class PluginTests
{
    private readonly Plugin _sut;
    private readonly IImporter _importer;
    private readonly IPluginLogger _logger;
    private readonly IWeatherApiImporterConfigurationLoader _weatherApiImporterConfigurationLoader;
    private readonly WeatherDataApiConfigurationDto _defaultApiConfiguration;
    private readonly WeatherDataApiConfigurationDto _fakeWeatherApiImporterConfigurationLoader;

    public PluginTests()
    {
        _importer = A.Fake<IImporter>();
        _logger = A.Fake<IPluginLogger>();
        _weatherApiImporterConfigurationLoader = A.Fake<IWeatherApiImporterConfigurationLoader>();

        _fakeWeatherApiImporterConfigurationLoader = A.Dummy<WeatherDataApiConfigurationDto>();

        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton(_importer);
        serviceCollection.AddSingleton(_logger);
        serviceCollection.AddSingleton(_weatherApiImporterConfigurationLoader);
        
        _defaultApiConfiguration = new WeatherDataApiConfigurationDto("https://arbitrary.com", "fakeApiKey", new List<Localisation>());

        var serviceProvider = serviceCollection.BuildServiceProvider();
        _sut = new Plugin(serviceProvider);
    }

    [Fact]
    public void Import_DelegatesImportingToImporter()
    {
        var tesImporterParameters = new TestImportParameters();
        
        // Arrange
        A.CallTo(() => _weatherApiImporterConfigurationLoader.Create(tesImporterParameters.LocationData, tesImporterParameters.FeedToImport.Source))
            .ReturnsLazily(() => _fakeWeatherApiImporterConfigurationLoader);
        
        A.CallTo(() => _importer.ImportAsync(_fakeWeatherApiImporterConfigurationLoader))
            .Returns(new ImportResult<UdcElement>() { Success = false });

        // Act
        var result = _sut.Import(CancellationToken.None, tesImporterParameters);

        // Assert
        result.Success.Should().BeFalse();
    }

    [Fact]
    public void Import_Fails_WhenImportingFails()
    {
        var tesImporterParameters = new TestImportParameters();

        // Arrange
        A.CallTo(() => _weatherApiImporterConfigurationLoader.Create(tesImporterParameters.LocationData, tesImporterParameters.FeedToImport.Source))
            .ReturnsLazily(() => _defaultApiConfiguration);
        
        A.CallTo(() => _importer.ImportAsync(_defaultApiConfiguration))
            .ThrowsAsync(() => new WeatherApiDataParserException("This failed."));

        // Act
        var result = _sut.Import(CancellationToken.None, tesImporterParameters);

        // Assert
        result.Success.Should().BeFalse();
    }

    [Fact]
    public void Metadata_IsSetToExpectedValues()
    {
        _sut.Guid.Should().Be(PluginMetadata.PluginGuid);
        _sut.Name.Should().Be(PluginMetadata.Name);
        _sut.NameShort.Should().Be(PluginMetadata.NameShort);
        _sut.VersionMajor.Should().Be(PluginMetadata.VersionMajor);
        _sut.VersionMinor.Should().Be(PluginMetadata.VersionMinor);

        _sut.FeedDefinitions.Should().HaveCount(1);
        var feedDefinition = _sut.FeedDefinitions.First();

        feedDefinition.Guid.Should().Be(PluginMetadata.FeedGuid);
        feedDefinition.Type.Should().Be(FeedDefinitionType.Web);
        feedDefinition.UpdateType.Should().Be(FeedUpdateType.Full);
    }
}
