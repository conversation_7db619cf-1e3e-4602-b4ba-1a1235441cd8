﻿namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin
{
    using System;
    using System.Diagnostics;

    public class PluginMetadata
    {
        public static readonly Guid PluginGuid = Guid.Parse("2e81d123-1bbf-4f59-84d3-518b7d99535d");

        public static readonly byte VersionMajor
            = Convert.ToByte(FileVersionInfo.GetVersionInfo(typeof(PluginMetadata).Assembly.Location).FileMajorPart);
        
        public static readonly byte VersionMinor
            = Convert.ToByte(FileVersionInfo.GetVersionInfo(typeof(PluginMetadata).Assembly.Location).FileMinorPart);

        public static readonly string Name = "Schindler Ahead Weather Api UDC Importer";

        public static readonly string NameShort = "WeatherApi";

        public static readonly Guid FeedGuid = Guid.Parse("82c09a01-b814-4c7a-8ddc-a4457c07814f");
    }
}