﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace SchindlerWeatherApiUdcPlugin.Tests.TestInfrastructure;

public class FakeWebhost : IDisposable
{
    private readonly IHost _host;
    private readonly FakeWebhostResponder _fakeWebhostResponder;
    private readonly string _url;

    public FakeWebhost(int port, string? hostname = null)
    {
        var host = hostname ?? "localhost";
        _url = $"http://{host}:{port}/";

        _host =
            Host.CreateDefaultBuilder()
                .ConfigureWebHostDefaults(
                    webHostBuilder =>
                    {
                        webHostBuilder
                            .ConfigureKestrel(o => o.AllowSynchronousIO = true)
                            .ConfigureServices(services => { services.AddSingleton<FakeWebhostResponder>(); })
                            .Configure(app =>
                            {
                                var responder = app.ApplicationServices.GetRequiredService<FakeWebhostResponder>();
                                app.Run(async context => await responder.RespondAsync(context));
                            })
                            .UseUrls(_url);
                    })
                .Build();

        _fakeWebhostResponder = _host.Services.GetRequiredService<FakeWebhostResponder>();

        _host.RunAsync();
    }

    public Uri Url => new(_url);

    public void ConfigureResponse(Func<HttpContext, Task> response) =>
        _fakeWebhostResponder.ConfigureResponse(response);

    public List<RecordedHttpRequest> RecordedHttpRequests => _fakeWebhostResponder.RecordedHttpRequests;

    public void Clear() => _fakeWebhostResponder.Clear();

    public void Dispose() => _host.Dispose();

    private class FakeWebhostResponder
    {
        private Func<HttpContext, Task>? _response;

        public readonly List<RecordedHttpRequest> RecordedHttpRequests = new();
        public void ConfigureResponse(Func<HttpContext, Task> response) => _response = response;

        public async Task RespondAsync(HttpContext context)
        {
            RecordedHttpRequests.Add(new RecordedHttpRequest(context.Request));

            if (_response is null)
            {
                RespondWithInternalServerError("No response set.", context);
                return;
            }

            await _response(context);
        }

        public void Clear()
        {
            RecordedHttpRequests.Clear();
        }

        private static void RespondWithInternalServerError(string message, HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            context.Response.StatusCode = 500;
            var sw = new StreamWriter(context.Response.Body);
            sw.Write(message);
            sw.Flush();
        }
    }

    public class RecordedHttpRequest(HttpRequest request)
    {
        public string Method { get; set; } = request.Method;
        public string AbsoluteUri { get; set; } = request.GetDisplayUrl();
    }
}
