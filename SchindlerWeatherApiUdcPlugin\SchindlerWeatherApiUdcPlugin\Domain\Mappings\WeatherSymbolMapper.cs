﻿using System;
using System.Collections.Generic;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Mappings
{
    public static class WeatherSymbolMapper
    {
        public static string MapWeatherApiCodeToForecaCode(string weatherApiCode)
        {
            var snowWeatherCodes = new List<string>()
            {
                "1066", "1210", "1213", "1216", "1219", "1255", "1069", "1072", "1168", "1171", "1198", "1201", "1204", "1207", "1249", "1252", "1114", "1117", "1222", "1225", "1258"
            };

            var result = (weatherApiCode) switch
            {
                "1000" or "" => "d000", // Sunny
                "1003" or "" => "d300", // Sun/Cloudy
                "1006" or "1009" or "1030" or "1135" or "1147" => "d400", // Cloudy
                "1087" or "1279" or "1276" or "1282" or "1273" => "d140", // Thunder
                "5000" => "d110", // SunAndRain
                "1063" or "1150" or "1153" or "1183" or "1186" or "1189" or "1240" or "1180" or "1192" or "1195" or "1243" or "1246" => "d120", // Rain
                var _ when snowWeatherCodes.Contains(weatherApiCode) => "d111", // Snow

                _ => throw new NotImplementedException($"For the following weather code: {weatherApiCode}, there is no mapping configuration!")
            };

            return result;
        }
    }
}
