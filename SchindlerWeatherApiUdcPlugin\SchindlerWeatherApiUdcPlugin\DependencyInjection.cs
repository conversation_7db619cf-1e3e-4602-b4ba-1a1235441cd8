﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Factories;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin
{
    public static class DependencyInjection
    {
        /// <summary>
        /// Registers all necessary classes for the Plugin
        /// </summary>
        /// <returns></returns>
        public static ServiceCollection CreateServiceCollection(PluginLoggingDelegates pluginLoggingDelegates)
        {
            var serviceCollection = new ServiceCollection();
            serviceCollection
                .AddTransient(_ => new HttpClient())
                .AddTransient<IWeatherApiImporterConfigurationLoader, WeatherApiImporterConfigurationLoader>()
                .AddTransient<IUdcWeatherDocumentFactory, UdcWeatherDocumentFactory>()
                .AddSingleton<IPluginLogger>(new PluginLogger(pluginLoggingDelegates))
                .AddSingleton<IImporter, Importer>()
                .AddSingleton<IWeatherDataApi, WeatherDataApi>();

            return serviceCollection;
        }
    }
}
