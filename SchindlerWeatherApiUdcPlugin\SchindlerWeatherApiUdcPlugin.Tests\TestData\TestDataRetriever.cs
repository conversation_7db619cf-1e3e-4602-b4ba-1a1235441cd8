﻿namespace SchindlerWeatherApiUdcPlugin.Tests.TestData;

using System.Reflection;

public class TestDataRetriever
{
    static TestDataRetriever()
    {
        var assemblyLocation = Assembly.GetExecutingAssembly().Location;
        var assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
        if (assemblyDirectory is null)
        {
            throw new DirectoryNotFoundException();
        }

        TestDataDirectory = Path.Combine(assemblyDirectory, "TestData");
    }

    private static string TestDataDirectory { get; set; }

    private static string GoodApiResponse => Path.Combine(TestDataDirectory, "GoodApiResponse.json");
    private static string EmptyApiResponse => Path.Combine(TestDataDirectory, "EmptyApiResponse.json");
    private static string InvalidApiResponse => Path.Combine(TestDataDirectory, "InvalidApiResponse.json");

    public static async Task<string> RetrieveTestData(TestData testData)
    {
        var path = testData switch
        {
            TestData.GoodApiResponse => GoodApiResponse,
            TestData.EmptyApiResponse => EmptyApiResponse,
            TestData.InvalidApiResponse => InvalidApiResponse,
            _ => throw new ArgumentOutOfRangeException(nameof(testData), testData, null)
        };

        return await File.ReadAllTextAsync(path);
    }

    public enum TestData
    {
        GoodApiResponse,
        EmptyApiResponse,
        InvalidApiResponse
    }
}
