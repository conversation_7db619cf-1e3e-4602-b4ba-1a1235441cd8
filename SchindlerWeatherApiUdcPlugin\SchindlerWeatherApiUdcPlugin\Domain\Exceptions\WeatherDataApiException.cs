﻿using System;
using System.Net;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions
{
    public class WeatherDataApiException : DomainException
    {
        public HttpStatusCode StatusCode { get; set; }

        public WeatherDataApiException(string message) : base(message)
        {
        }

        public WeatherDataApiException(string message, HttpStatusCode statusCode) : base(message)
        {
            StatusCode = statusCode;
        }

        public WeatherDataApiException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
