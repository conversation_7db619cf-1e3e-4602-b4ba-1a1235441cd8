﻿namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class Weather
    {
        [JsonProperty("location")]
        public WeatherLocation Location { get; set; }

        [JsonProperty("current")]
        public CurrentWeatherPrognose Current { get; set; }

        [JsonProperty("forecast")]
        public WeatherForecast Forecast { get; set; }
    }

    public sealed class WeatherLocation
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("region")]
        public string Region { get; set; }

        [JsonProperty("country")]
        public string Country { get; set; }

        [JsonProperty("lat")]
        public string Latitude { get; set; }

        [JsonProperty("lon")]
        public string Longitude { get; set; }

        [JsonProperty("tz_id")]
        public string TzId { get; set; }

        [JsonProperty("localtime")]
        public DateTime LocalTime { get; set; }
    }

    public sealed class CurrentWeatherPrognose
    {
        [JsonProperty("last_updated")]
        public string Time { get; set; }

        [JsonProperty("temp_c")]
        public double Temp { get; set; }

        [JsonProperty("feelslike_c")]
        public double FeelsLikeTemp { get; set; }

        [JsonProperty("humidity")]
        public int Humidity { get; set; }

        [JsonProperty("dewpoint_c")]
        public double DewPoint { get; set; }

        [JsonProperty("wind_kph")]
        public double WindSpeed { get; set; }

        [JsonProperty("wind_dir")]
        public string WindDirString { get; set; }

        [JsonProperty("gust_kph")]
        public double WindGust { get; set; }

        [JsonProperty("precip_mm")]
        public double Precip { get; set; }

        [JsonProperty("cloud")]
        public int Cloudiness { get; set; }

        [JsonProperty("uv")]
        public double UvIndex { get; set; }

        [JsonProperty("pressure_mb")]
        public double Pressure { get; set; }

        [JsonProperty("vis_km")]
        public double Visibility { get; set; }

        [JsonProperty("condition")]
        public WeatherCondition Condition { get; set; }
    }

    public sealed class WeatherForecast
    {
        [JsonProperty("forecastday")]
        public WeatherForecastDay[] ForecastDays { get; set; }
    }

    public sealed class WeatherForecastDay
    {
        [JsonProperty("date")]
        public string Date { get; set; }

        [JsonProperty("day")]
        public Day Day { get; set; }

        [JsonProperty("astro")]
        public ForecastDayAstroData Astro {  get; set; }

        // only in hourly: min/max feels like temp; min/maxRelHumidity; min/max DewPoint; windDirection; maxwindGust; cloudiness; sunriseEpoch; sunsetEpoch; minVisibility; pressure  
        [JsonProperty("hour")]
        public List<ForecastDayHour> Hours { get; set; }
    }

    public sealed class Day 
    {
        [JsonProperty("mintemp_c")]
        public double MinTemp { get; set; }

        [JsonProperty("maxtemp_c")]
        public double MaxTemp { get; set; }

        [JsonProperty("totalprecip_mm")]
        public double PrecipAccum { get; set; }

        [JsonProperty("maxwind_kph")]
        public double MaxWindSpeed { get; set; }

        [JsonProperty("uv")]
        public double UvIndex { get; set; }

        [JsonProperty("condition")]
        public WeatherCondition Condition { get; set; }
    }

    public sealed class ForecastDayAstroData
    {
        [JsonProperty("sunrise")]
        public string Sunrise { get; set; }

        [JsonProperty("sunset")]
        public string Sunset { get; set; }

        [JsonProperty("moonrise")]
        public string Moonrise { get; set; }

        [JsonProperty("moonset")]
        public string Moonset { get; set; }
    }

    public sealed class WeatherCondition
    {
        [JsonProperty("text")]
        public string Text { get; set; }

        [JsonProperty("code")]
        public string Code { get; set; }
    }

    public sealed class ForecastDayHour
    {
        [JsonProperty("feelslike_c")]
        public double FeelsLikeTemp { get; set; }

        [JsonProperty("humidity")]
        public int Humidity { get; set; }

        [JsonProperty("dewpoint_c")]
        public double DewPoint { get; set; }

        [JsonProperty("wind_degree")]
        public int WindDegree { get; set; }

        [JsonProperty("gust_kph")]
        public double WindGust { get; set; }

        [JsonProperty("cloud")]
        public int Cloudiness { get; set; }

        [JsonProperty("vis_km")]
        public double Visibility { get; set; }

        [JsonProperty("pressure_mb")]
        public double Pressure { get; set; }

        [JsonProperty("chance_of_rain")]
        public int ChanceOfRain {  get; set; } 
    }
}
