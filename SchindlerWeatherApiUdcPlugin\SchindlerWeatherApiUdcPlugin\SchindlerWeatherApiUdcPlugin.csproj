<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net462</TargetFramework>
		<LangVersion>latest</LangVersion>
		<RootNamespace>Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin</RootNamespace>
		<AssemblyName>SchindlerWeatherApiUdcPlugin</AssemblyName>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
		<OutputType>Library</OutputType>
		<NoWarn>CS1998</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Grassfish.UDC.PluginBase" Version="1.0.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
	</ItemGroup>

	<ItemGroup>
		<Reference Include="System.ComponentModel.Composition" />
		<Reference Include="System.Net.Http" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Properties\"/>
	</ItemGroup>

</Project>