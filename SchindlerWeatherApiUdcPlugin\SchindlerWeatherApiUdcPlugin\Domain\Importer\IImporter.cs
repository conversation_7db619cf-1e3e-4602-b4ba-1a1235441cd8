﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.UDC.PluginBase.V2;
using System.Threading.Tasks;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer
{
    public interface IImporter
    {
        Task<ImportResult<UdcElement>> ImportAsync(WeatherDataApiConfigurationDto weatherDataApiConfiguration);
    }
}
