﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;

using Microsoft.Extensions.Logging;
using SchindlerWeatherApiUdcPlugin.Debugger;

using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger("Program");
try
{
    var locationData = new List<LocationData>()
    {
        new LocationData() 
        { 
            LocationId = 1,
            Address = "Muenchen",
            Latitude = 50.113754,
            Longitude = 8.661072,
            Country = "TestCountry"
        }
    };

    var parameters = new ImportParameters();
    parameters.LocationData = locationData;

    var serviceCollection = DependencyInjection.CreateServiceCollection(new PluginLoggingDelegates(
        (s, o) => Console.WriteLine(s),
        (s, o) => Console.WriteLine(s),
        (s, o) => Console.WriteLine(s),
        (s, o) => Console.WriteLine(s)
    ));

    var plugin = new Plugin();

    var result = plugin.Import(CancellationToken.None, parameters);

    var elements = result.GetElementsToStore();
    foreach (var element in elements)
    {
        Console.WriteLine("Element:" + element.Name);
    }
}
catch (Exception ex)
{
    logger.LogCritical(exception: ex, "Caught critical error.");
}

namespace SchindlerWeatherApiUdcPlugin.Debugger
{
    using Grassfish.UDC.PluginBase.Enums;
    using Grassfish.UDC.PluginBase.V2.Interfaces;

    class ImportParameters : IImportParameters<ICustomer?, IFeed, ILocationData>
    {
        public ICustomer? Customer => null;
        public IFeed FeedToImport => new Feed();
        public string WorkingDirectory { get; } = string.Empty;
        public IEnumerable<ILocationData> LocationData { get; set; } = [];
    }

    class Feed : IFeed
    {
        public Guid DefinitionGuid { get; }
        public string Name { get; } = "My Test Feed";
        public FeedDefinitionType Type => FeedDefinitionType.Web;
        public FeedUpdateType UpdateType => FeedUpdateType.Full;
        public string Source => "config.json";
        public string Username => string.Empty;
        public string Password => string.Empty;
        public string SpecificValue { get; } = string.Empty;
    }

    public sealed class LocationData : ILocationData
    {
        public int LocationId { get; set; }
        public string BoxId { get; set; }
        public string Name { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string Country { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public string Address { get; set; }
        public Guid? EditionGuid { get; set; }
        public IEnumerable<string> Tags { get; set; }
        public IReadOnlyDictionary<string, string> CustomValues { get; set; }
    }
}