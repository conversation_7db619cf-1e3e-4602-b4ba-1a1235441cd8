﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Newtonsoft.Json;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi
{
    public static class WeatherApiDataParser
    {
        public static Weather ParseApiResponse(string apiResponse)
        {
            try
            {
                var result = JsonConvert.DeserializeObject<Weather>(apiResponse);

                return result;
            }
            catch (JsonException ex)
            {
                throw new WeatherApiDataParserException("Failed to parse API response.", ex);
            }
        }
    }
}
