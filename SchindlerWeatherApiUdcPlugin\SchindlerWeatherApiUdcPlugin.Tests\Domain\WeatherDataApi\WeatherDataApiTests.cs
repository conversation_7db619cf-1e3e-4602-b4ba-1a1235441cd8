﻿using System.Net;
using FakeItEasy;
using FluentAssertions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Microsoft.Extensions.DependencyInjection;
using SchindlerWeatherApiUdcPlugin.Tests.Mock;
using SchindlerWeatherApiUdcPlugin.Tests.TestData;
using SchindlerWeatherApiUdcPlugin.Tests.TestInfrastructure;
using Xunit;

namespace SchindlerWeatherApiUdcPlugin.Tests.Domain.WeatherDataApi;

public class WeatherDataApiTests : IDisposable
{
    private readonly Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi.WeatherDataApi _sut;
    private readonly MockImporterConfiguration _defaultApiConfiguration;
    private readonly FakeWebhost _fakeWebhost; 
    private readonly IPluginLogger _logger;

    public WeatherDataApiTests()
    {
        _logger = A.Fake<IPluginLogger>();
        
        var services = new ServiceCollection();
        services.AddHttpClient();
        services
            .AddSingleton<Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi.WeatherDataApi>();
        services.AddSingleton(_logger);
        var sp = services.BuildServiceProvider();

        _fakeWebhost = new FakeWebhost(9999);
        _defaultApiConfiguration = new MockImporterConfiguration();
        _defaultApiConfiguration.Endpoint = new Uri(_fakeWebhost.Url.ToString());

        _sut = sp
            .GetRequiredService<Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi.WeatherDataApi>();
    }

    [Fact]
    public async Task RetrieveWeathers_WithGoodApiResponse_ReturnsWeathersCollection()
    {
        // Arrange
        SetupGoodApiResponse();

        // Act
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task RetrieveWeathers_WithEmptyApiResponse_ReturnsWeathersCollection()
    {
        // Arrange
        SetupApiResponse(TestDataRetriever.TestData.EmptyApiResponse);

        // Act
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);

        // Assert
        result.Current.Should().BeNull();
        result.Forecast.Should().BeNull();
        result.Location.Should().BeNull();
    }

    [Fact]
    public async Task RetrieveWeathers_ShouldCallApi()
    {
        // Arrange
        SetupGoodApiResponse();

        // Act
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);

        // Assert
        _fakeWebhost.RecordedHttpRequests.Should().NotBeEmpty();
    }

    [Fact]
    public async Task RetrieveWeathers_WithGoodApiResponse_ShouldRetrieveDataFromApi()
    {
        // Arrange
        SetupGoodApiResponse();

        // Act
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);

        // Assert
        result.Should().BeEquivalentTo(
            new Weather()
            {
                Location = new WeatherLocation()
                {
                    Name = "Whitehall",
                    Region = "Ohio",
                    Country = "United States of America",
                    Latitude = "39.967",
                    Longitude = "-82.886",
                    TzId = "America/New_York",
                    LocalTime = DateTime.Parse("2024-12-04 02:42"),
                },
                Current = new CurrentWeatherPrognose()
                {
                    Time = "2024-12-04 02:30",
                    Temp = -4.9,
                    FeelsLikeTemp = -10.6,
                    Humidity = 81,
                    DewPoint = -7.3,
                    WindSpeed = 15.5,
                    WindDirString = "SSW",
                    WindGust = 22.0,
                    Precip = 0.0,
                    Cloudiness = 75,
                    UvIndex = 0.0,
                    Pressure = 1026.0,
                    Visibility = 16.0,
                    Condition = new WeatherCondition()
                    {
                        Text = "Partly cloudy",
                        Code = "1003"
                    }
                },
                Forecast = new WeatherForecast()
                {
                    ForecastDays =
                    [
                        new WeatherForecastDay()
                        {
                            Date = "2024-12-04",
                            Day = new Day()
                            {
                                MinTemp = -4.5,
                                MaxTemp = 1.2,
                                PrecipAccum = 0.0,
                                MaxWindSpeed = 31.3,
                                UvIndex = 0.2,
                                Condition = new WeatherCondition()
                                {
                                    Text = "Overcast",
                                    Code = "1009"
                                }
                            },
                            Astro = new ForecastDayAstroData()
                            {
                                Sunrise = "07:38 AM",
                                Sunset = "05:06 PM",
                                Moonrise = "10:57 AM",
                                Moonset = "08:06 PM"
                            },
                            Hours =
                            [
                                new ForecastDayHour()
                                {
                                    FeelsLikeTemp = -9.4,
                                    Humidity = 79,
                                    DewPoint = -7.0,
                                    WindDegree = 216,
                                    WindGust = 20.3,
                                    Cloudiness = 62,
                                    Visibility = 10.0,
                                    Pressure = 1030.0,
                                    ChanceOfRain = 0
                                },
                                new ForecastDayHour()
                                {
                                    FeelsLikeTemp = -9.8,
                                    Humidity = 81,
                                    DewPoint = -7.2,
                                    WindDegree = 213,
                                    WindGust = 21.2,
                                    Cloudiness = 54,
                                    Visibility = 10.0,
                                    Pressure = 1029.0,
                                    ChanceOfRain = 0
                                }
                            ]
                        }
                    ]
                }
            });
    }

    [Fact]
    public async Task RetrieveWeathers_ShouldRetrieveDataFromConfiguredEndpoint()
    {
        // Arrange
        SetupGoodApiResponse();

        // Act
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);

        // Assert
        _fakeWebhost.RecordedHttpRequests.Should().Contain(t =>
            t.Method == "GET" && t.AbsoluteUri == string.Concat(_defaultApiConfiguration.Endpoint.AbsoluteUri, "?key=00000000-0000-0000-0000-000000000000&q=39.97,-82.89&days=7"));
    }

    [Fact]
    public async Task RetrieveWeathers_WithInvalidApiResponse()
    {
        // Arrange
        SetupApiResponse(TestDataRetriever.TestData.InvalidApiResponse);

        // Act & Assert
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);

        result.Should().BeNull();
    }

    [Theory]
    [InlineData(HttpStatusCode.BadRequest)]
    [InlineData(HttpStatusCode.BadGateway)]
    [InlineData(HttpStatusCode.InternalServerError)]
    [InlineData(HttpStatusCode.Forbidden)]
    [InlineData(HttpStatusCode.Gone)]
    public async Task RetrieveWeathers_WithBadRequestApiResponse(
        HttpStatusCode statusCode)
    {
        // Arrange
        SetupFailingApiResponse(statusCode);

        // Act & Assert
        var result = await _sut.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0]);
        
        result.Should().BeNull();
    }

    private void SetupGoodApiResponse()
    {
        SetupApiResponse(TestDataRetriever.TestData.GoodApiResponse);
    }

    private void SetupApiResponse(TestDataRetriever.TestData responseType)
    {
        _fakeWebhost.ConfigureResponse(async context =>
        {
            var apiResponse = await TestDataRetriever.RetrieveTestData(responseType);

            context.Response.StatusCode = 200;
            context.Response.ContentType = "application/json";
            await using var writer = new StreamWriter(context.Response.Body);
            await writer.WriteAsync(apiResponse);
        });
    }

    private void SetupFailingApiResponse(HttpStatusCode statusCode)
    {
        _fakeWebhost.ConfigureResponse(context =>
        {
            context.Response.StatusCode = (int)statusCode;
            return Task.CompletedTask;
        });
    }

    public void Dispose()
    {
        _fakeWebhost.Dispose();
    }
}