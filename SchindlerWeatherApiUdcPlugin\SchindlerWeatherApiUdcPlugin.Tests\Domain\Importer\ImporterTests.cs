﻿using System.Net;
using FakeItEasy;
using FluentAssertions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Factories;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using SchindlerWeatherApiUdcPlugin.Tests.Mock;
using Xunit;

namespace SchindlerWeatherApiUdcPlugin.Tests.Domain.Importer;

public class ImporterTests
{
    private readonly Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Importer _sut;
    private readonly IWeatherDataApi _weatherDataApi;
    private readonly IPluginLogger _logger;
    private readonly IUdcWeatherDocumentFactory _udcWeatherDocumentFactory;
    private readonly WeatherDataApiConfigurationDto _defaultApiConfiguration;

    public ImporterTests()
    {
        _weatherDataApi = A.Fake<IWeatherDataApi>();
        _logger = A.Fake<IPluginLogger>();
        _udcWeatherDocumentFactory = A.Fake<IUdcWeatherDocumentFactory>();

        var mockLocalisations = new List<Localisation>
        {
            new()
            {
                City = "Whitehall",
                Country = "United States of America",
                Lat = 39.967,
                Long = -82.886,
                ZipCode = "12345",
            }
        };
        
        _defaultApiConfiguration = new WeatherDataApiConfigurationDto("https://arbitrary.com", "fakeApiKey", mockLocalisations);
        _defaultApiConfiguration.Locations = new List<ILocationData>()
        {
            new LocationData()
            {
                LocationId = 1,
                City = "Whitehall",
                Country = "United States of America",
                Latitude = 39.967,
                Longitude = -82.886,
                ZipCode = "12345",
            }
        };
        
        _sut = new Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Importer(_weatherDataApi, _logger, _udcWeatherDocumentFactory);
    }

    [Fact]
    public async Task ImportAsync_RetrievesWeathersFromApi()
    {
        var weather = new Weather
        {
            Location = new WeatherLocation
            {
                Name = "Whitehall",
                Region = "Ohio",
                Country = "United States of America",
                Latitude = "39.967",
                Longitude = "-82.886",
                TzId = "America/New_York",
                LocalTime = DateTime.Parse("2024-12-04 02:42"),
            },
            Current = new CurrentWeatherPrognose
            {
                Time = "2024-12-04 02:30",
                Temp = -4.9,
                FeelsLikeTemp = -10.6,
                Humidity = 81,
                DewPoint = -7.3,
                WindSpeed = 15.5,
                WindDirString = "SSW",
                WindGust = 22.0,
                Precip = 0.0,
                Cloudiness = 75,
                UvIndex = 0.0,
                Pressure = 1026.0,
                Visibility = 16.0,
                Condition = new WeatherCondition
                {
                    Text = "Partly cloudy",
                    Code = "1003"
                }
            },
            Forecast = new WeatherForecast
            {
                ForecastDays =
                [
                    new WeatherForecastDay
                    {
                        Date = "2024-12-04",
                        Day = new Day
                        {
                            MinTemp = -4.5,
                            MaxTemp = 1.2,
                            PrecipAccum = 0.0,
                            MaxWindSpeed = 31.3,
                            UvIndex = 0.2,
                            Condition = new WeatherCondition
                            {
                                Text = "Partly cloudy",
                                Code = "1003"
                            }
                        },
                        Astro = new ForecastDayAstroData
                        {
                            Sunrise = "07:38 AM",
                            Sunset = "05:06 PM",
                            Moonrise = "10:57 AM",
                            Moonset = "08:06 PM"
                        },
                        Hours =
                        [
                            new ForecastDayHour
                            {
                                FeelsLikeTemp = -9.4,
                                Humidity = 79,
                                DewPoint = -7.0,
                                WindDegree = 216,
                                WindGust = 20.3,
                                Cloudiness = 62,
                                Visibility = 10.0,
                                Pressure = 1030.0,
                                ChanceOfRain = 0
                            },
                            new ForecastDayHour
                            {
                                FeelsLikeTemp = -9.8,
                                Humidity = 81,
                                DewPoint = -7.2,
                                WindDegree = 213,
                                WindGust = 21.2,
                                Cloudiness = 54,
                                Visibility = 10.0,
                                Pressure = 1029.0,
                                ChanceOfRain = 0
                            }
                        ]
                    }
                ]
            }
        };
        
        // Arrange
        SetupWeathersReturnedByApi(weather);

        // Act
        var result = await _sut.ImportAsync(_defaultApiConfiguration);

        // Assert
        result.Should().NotBeNull();

        A.CallTo(() =>
                _weatherDataApi.RetrieveWeatherDataAsync(
                    _defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0], A<CancellationToken>._))
            .MustHaveHappened();
    }

    [Fact]
    public async Task ImportAsync_HandlesImportErrors()
    {
        // Arrange
        SetupThrowingApi();

        // Act
        var result = await _sut.ImportAsync(_defaultApiConfiguration);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();

        A.CallTo(() => _logger.LogError("Caught exception while attempting to import the measurements.", A<Exception>._))
            .MustHaveHappened();
    }

    [Fact]
    public async Task ImportAsync_ShouldAddWeathersToImportResult()
    {
        // Arrange
        var weather = new Weather
        {
            Location = new WeatherLocation
            {
                Name = "Whitehall",
                Region = "Ohio",
                Country = "United States of America",
                Latitude = "39.967",
                Longitude = "-82.886",
                TzId = "America/New_York",
                LocalTime = DateTime.Parse("2024-12-04 02:42"),
            },
            Current = new CurrentWeatherPrognose
            {
                Time = "2024-12-04 02:30",
                Temp = -4.9,
                FeelsLikeTemp = -10.6,
                Humidity = 81,
                DewPoint = -7.3,
                WindSpeed = 15.5,
                WindDirString = "SSW",
                WindGust = 22.0,
                Precip = 0.0,
                Cloudiness = 75,
                UvIndex = 0.0,
                Pressure = 1026.0,
                Visibility = 16.0,
                Condition = new WeatherCondition
                {
                    Text = "Partly cloudy",
                    Code = "1003"
                }
            },
            Forecast = new WeatherForecast
            {
                ForecastDays =
                [
                    new WeatherForecastDay
                    {
                        Date = "2024-12-04",
                        Day = new Day
                        {
                            MinTemp = -4.5,
                            MaxTemp = 1.2,
                            PrecipAccum = 0.0,
                            MaxWindSpeed = 31.3,
                            UvIndex = 0.2,
                            Condition = new WeatherCondition
                            {
                                Text = "Partly cloudy",
                                Code = "1003"
                            }
                        },
                        Astro = new ForecastDayAstroData
                        {
                            Sunrise = "07:38 AM",
                            Sunset = "05:06 PM",
                            Moonrise = "10:57 AM",
                            Moonset = "08:06 PM"
                        },
                        Hours =
                        [
                            new ForecastDayHour
                            {
                                FeelsLikeTemp = -9.4,
                                Humidity = 79,
                                DewPoint = -7.0,
                                WindDegree = 216,
                                WindGust = 20.3,
                                Cloudiness = 62,
                                Visibility = 10.0,
                                Pressure = 1030.0,
                                ChanceOfRain = 0
                            },
                            new ForecastDayHour
                            {
                                FeelsLikeTemp = -9.8,
                                Humidity = 81,
                                DewPoint = -7.2,
                                WindDegree = 213,
                                WindGust = 21.2,
                                Cloudiness = 54,
                                Visibility = 10.0,
                                Pressure = 1029.0,
                                ChanceOfRain = 0
                            }
                        ]
                    }
                ]
            }
        };
        SetupWeathersReturnedByApi(weather);

        var feed = new MockFeed();

        // Act
        var result = await _sut.ImportAsync(_defaultApiConfiguration);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.GetElementsToStore().Should().HaveCount(1);
    }

    [Fact]
    public async Task ImportAsync_ShouldAddUdcElementToStore()
    {
        // Arrange
        var weather = new Weather {  Location = new WeatherLocation { Latitude = "39.967", Longitude = "-82.886" }};
        SetupWeathersReturnedByApi(weather);

        // Act
        var result = await _sut.ImportAsync(_defaultApiConfiguration);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.GetElementsToStore().Should().NotBeEmpty();
    }


    private void SetupWeathersReturnedByApi(Weather weather)
    {
        A.CallTo(() =>
                _weatherDataApi.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0], A<CancellationToken>._))
            .ReturnsLazily(() => weather);
    }

    private void SetupThrowingApi()
    {
        A.CallTo(() =>
                _weatherDataApi.RetrieveWeatherDataAsync(_defaultApiConfiguration.Endpoint, _defaultApiConfiguration.ApiKey, _defaultApiConfiguration.Localisations[0], A<CancellationToken>._))
            .ThrowsAsync(() => new WeatherDataApiException("I am sad.", HttpStatusCode.Gone));
    }
}
