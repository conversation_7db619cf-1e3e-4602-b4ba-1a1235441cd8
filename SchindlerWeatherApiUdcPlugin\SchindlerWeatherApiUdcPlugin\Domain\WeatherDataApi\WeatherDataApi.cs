﻿using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Exceptions;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.Importer.Models;
using System;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Logging;

namespace Grassfish.Customers.Schindler.Udc.SchindlerWeatherApiUdcPlugin.Domain.WeatherDataApi
{
    public class WeatherDataApi : IWeatherDataApi
    {
        private readonly HttpClient _httpClient;
        private readonly IPluginLogger _logger;

        public WeatherDataApi(HttpClient httpClient, 
            IPluginLogger logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<Weather> RetrieveWeatherDataAsync(
        Uri endpoint, string apiKey, Localisation localization, CancellationToken cancellationToken = default)
        {
            try
            {
                var requestUrlStringBuilder = new StringBuilder(endpoint.ToString());

                var latitude = localization.Lat.ToString().Contains(",")
                    ? localization.Lat.ToString().Replace(",", ".")
                    : localization.Lat.ToString();
                var longitude = localization.Long.ToString().Contains(",")
                    ? localization.Long.ToString().Replace(",", ".")
                    : localization.Long.ToString();

                requestUrlStringBuilder
                    .Append($"?key={apiKey}")
                    .Append($"&q={latitude},{longitude}")
                    .Append("&days=7");

                var result = await _httpClient.GetAsync(requestUrlStringBuilder.ToString(), cancellationToken);
                if (!result.IsSuccessStatusCode)
                {
                    _logger.LogError(new WeatherDataApiException(
                        $"Failed to retrieve weather data from API with the following StatusCode: {result.StatusCode}. With the following URL: {requestUrlStringBuilder}"));
                    
                    return null;
                }

                var stringResult = await result.Content.ReadAsStringAsync();

                return WeatherApiDataParser.ParseApiResponse(stringResult);
            }
            catch (Exception e)
            {
                _logger.LogError(new WeatherDataApiException(
                    $"Failed to retrieve weather data from API with the following error message: {e}"));
            }
            
            return null;
        }
    }
}
